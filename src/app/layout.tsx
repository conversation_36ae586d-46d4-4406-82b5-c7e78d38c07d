import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-inter',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Vitaliti Air - Revitalizing Human Energy',
  description: 'Revolutionary altitude training technology that delivers the benefits of exercise without exercise. Just 30 minutes a day, 3 times a week. Scientifically proven to increase VO2 max and extend healthspan.',
  keywords: [
    'altitude training',
    'VO2 max',
    'hypoxic training',
    'mitochondrial health',
    'longevity',
    'healthspan',
    'cellular regeneration',
    'metabolic health',
    'cardiovascular fitness',
    'Vitaliti Air'
  ],
  authors: [{ name: 'Vitaliti Air' }],
  creator: 'Vitaliti Air',
  publisher: 'Vitaliti Air',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://vitaliti-air.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Vitaliti Air - Revitalizing Human Energy',
    description: 'Revolutionary altitude training technology that delivers the benefits of exercise without exercise. Just 30 minutes a day, 3 times a week.',
    url: 'https://vitaliti-air.com',
    siteName: 'Vitaliti Air',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Vitaliti Air - Revitalizing Human Energy. One cell at a time.',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vitaliti Air - Revitalizing Human Energy',
    description: 'Revolutionary altitude training technology that delivers the benefits of exercise without exercise. Just 30 minutes a day, 3 times a week.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/images/logo.png" sizes="32x32" type="image/png" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/images/logo.png" sizes="180x180" />
        <meta name="theme-color" content="#2563eb" />
      </head>
      <body className={inter.variable}>{children}</body>
    </html>
  )
}
