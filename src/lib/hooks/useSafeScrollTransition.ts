// Safer scroll transition hook that doesn't lock body scroll
import { useScroll, useTransform, useSpring } from 'framer-motion'
import { useEffect, useState, useCallback } from 'react'

interface UseSafeScrollTransitionOptions {
  threshold?: number
}

export function useSafeScrollTransition(
  options: UseSafeScrollTransitionOptions = {}
) {
  const { 
    threshold = 50
  } = options

  const [hasTriggered, setHasTriggered] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const { scrollY } = useScroll()
  
  // Get viewport height safely
  const [viewportHeight, setViewportHeight] = useState(800)
  
  // Create scroll-driven progress that automatically reverses
  const rawProgress = useTransform(
    scrollY,
    [0, viewportHeight],
    [0, 1],
    { clamp: true }
  )
  const smoothProgress = useSpring(rawProgress, { 
    stiffness: 50, 
    damping: 20 
  })
  
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setViewportHeight(window.innerHeight)
      
      const handleResize = () => setViewportHeight(window.innerHeight)
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Transform values based on scroll position and transition progress
  const heroY = useTransform(
    smoothProgress,
    [0, 1],
    [0, -viewportHeight]
  )

  const heroOpacity = useTransform(
    smoothProgress,
    [0, 0.5],
    [1, 0]
  )

  const section2Y = useTransform(
    smoothProgress,
    [0, 1],
    [viewportHeight, 0]
  )

  const section2Opacity = useTransform(
    smoothProgress,
    [0.5, 1],
    [0, 1]
  )

  // Handle scroll indicator visibility
  useEffect(() => {
    const unsubscribe = scrollY.onChange((latest) => {
      const scrollIndicator = document.querySelector('[data-scroll-indicator]')
      if (scrollIndicator) {
        // Hide indicator when scrolling down, show when at top
        (scrollIndicator as HTMLElement).style.opacity = latest > threshold ? '0' : '1'
      }
    })
    
    return unsubscribe
  }, [scrollY, threshold])

  // Reset function
  const resetTransition = useCallback(() => {
    setHasTriggered(false)
    setIsTransitioning(false)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [])

  return {
    // Transform values for hero section
    heroTransforms: {
      y: heroY,
      opacity: heroOpacity,
      scale: useTransform(smoothProgress, [0, 1], [1, 0.95])
    },
    
    // Transform values for section 2
    section2Transforms: {
      y: section2Y,
      opacity: section2Opacity
    },
    
    // State
    hasTriggered,
    isTransitioning,
    
    // Control
    resetTransition
  }
}
