"use client"

import { motion } from "framer-motion"
import { getSectionContent, ProductOverviewContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, staggerContainer, getAnimationVariants } from "@/lib/animations"
import { GlowCard } from "@/components/ui/glow-card"

export function ProductOverviewSection() {
  const content = getSectionContent(2) as ProductOverviewContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.2 })

  return (
    <section
      id="product-overview"
      className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center relative"
      style={{
        backgroundImage: 'url(/images/product-overview-background.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background overlay for better text readability */}
      <div className="absolute inset-0 bg-black/70"></div>
      <motion.div
        ref={ref}
        className="max-w-6xl mx-auto w-full relative z-10"
        variants={getAnimationVariants(staggerContainer)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        <motion.h2
          className="text-4xl md:text-5xl font-bold text-center mb-16 text-white"
          variants={getAnimationVariants(fadeInUp)}
        >
          {content.title}
        </motion.h2>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12"
          variants={getAnimationVariants(staggerContainer)}
        >
          {content.features.map((feature, index) => (
            <motion.div
              key={index}
              className="flex justify-center"
              variants={getAnimationVariants(fadeInUp)}
            >
              <GlowCard
                customSize={true}
                className="w-full max-w-sm h-64 flex flex-col items-center justify-center text-center"
                glowColor="blue"
              >
                <div className="flex flex-col items-center justify-center h-full space-y-4">
                  <h3 className="text-2xl md:text-3xl font-bold text-white">
                    {feature.title}
                  </h3>
                  <p className="text-lg text-gray-200">
                    {feature.description}
                  </p>
                </div>
              </GlowCard>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </section>
  )
}
