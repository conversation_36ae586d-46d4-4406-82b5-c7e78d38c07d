"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { X, Menu } from "lucide-react"

interface HeaderProps {
  className?: string
}

export function Header({ className }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Handle scroll effect for header background
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Navigation items
  const navItems = [
    { label: "How It Works", href: "#how-it-works" },
    { label: "Science", href: "#science" },
    { label: "Team", href: "#team" },
  ]

  // Smooth scroll function
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMobileMenuOpen(false) // Close mobile menu after navigation
  }

  return (
    <motion.header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled
          ? "bg-white/10 backdrop-blur-md border-b border-white/10"
          : "bg-transparent",
        className
      )}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container mx-auto px-8 md:px-16 lg:px-24">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo Section */}
          <motion.button
            onClick={scrollToTop}
            className="flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="relative">
              <Image
                src="/images/logo.png"
                alt="Vitaliti Air Logo"
                width={40}
                height={40}
                className="w-8 h-8 md:w-10 md:h-10 object-contain"
                priority
              />
            </div>
            <span 
              className="text-xl md:text-2xl font-bold tracking-tight"
              style={{ color: 'var(--color-white)' }}
            >
              Vitaliti Air
            </span>
          </motion.button>

          {/* Navigation - Desktop */}
          <motion.nav
            className="hidden md:flex items-center space-x-8"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {navItems.map((item, index) => (
              <motion.button
                key={item.label}
                onClick={() => scrollToSection(item.href)}
                className="text-sm font-medium transition-colors duration-200 hover:text-white/80"
                style={{ color: 'var(--color-white)' }}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
              >
                {item.label}
              </motion.button>
            ))}
          </motion.nav>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Button
              size="sm"
              onClick={() => scrollToSection("#waitlist")}
              className={cn(
                "bg-white/10 backdrop-blur-md border border-white/20",
                "text-white hover:bg-white/20 hover:border-white/30",
                "transition-all duration-200",
                "rounded-lg px-4 py-2 text-sm font-medium"
              )}
            >
              Join Waitlist
            </Button>
          </motion.div>

          {/* Mobile Menu Button */}
          <motion.button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/20"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.7 }}
          >
            {isMobileMenuOpen ? (
              <X className="w-5 h-5" style={{ color: 'var(--color-white)' }} />
            ) : (
              <Menu className="w-5 h-5" style={{ color: 'var(--color-white)' }} />
            )}
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="fixed inset-0 z-40 md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Backdrop */}
            <motion.div
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
              onClick={() => setIsMobileMenuOpen(false)}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />
            
            {/* Menu Content */}
            <motion.div
              className="absolute top-16 left-0 right-0 bg-black/90 backdrop-blur-md border-b border-white/10"
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -20, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <div className="container mx-auto px-8">
                <div className="py-6 space-y-4">
                  {navItems.map((item, index) => (
                    <motion.button
                      key={item.label}
                      onClick={() => scrollToSection(item.href)}
                      className="block w-full text-left py-3 px-4 text-base font-medium transition-colors duration-200 hover:bg-white/10 rounded-lg"
                      style={{ color: 'var(--color-white)' }}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      {item.label}
                    </motion.button>
                  ))}
                  
                  {/* Mobile CTA Button */}
                  <motion.div
                    className="pt-4"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.3, delay: navItems.length * 0.1 }}
                  >
                    <Button
                      size="sm"
                      onClick={() => scrollToSection("#waitlist")}
                      className={cn(
                        "w-full bg-white/10 backdrop-blur-md border border-white/20",
                        "text-white hover:bg-white/20 hover:border-white/30",
                        "transition-all duration-200",
                        "rounded-lg px-4 py-3 text-base font-medium"
                      )}
                    >
                      Join Waitlist
                    </Button>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  )
}
