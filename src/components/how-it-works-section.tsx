"use client"

import { motion } from "framer-motion"
import { getS<PERSON><PERSON><PERSON>ontent, HowItWorksContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, getAnimationVariants } from "@/lib/animations"
import { FeatureCarousel } from "@/components/ui/animated-feature-carousel"

interface Step {
  id: string
  name: string
  title: string
  description: string
}

interface ImageSet {
  step1img1: string
  step1img2: string
  step2img1: string
  step2img2: string
  step3img: string
  alt: string
}

export function HowItWorksSection() {
  const content = getSectionContent(4) as HowItWorksContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.2 })

  // Transform the content data to match the carousel format (only 3 steps)
  const steps: Step[] = content.steps.map((step) => ({
    id: step.number.toString(),
    name: `Step ${step.number}`,
    title: step.title,
    description: step.text
  }))

  // Image set using local step images from public/images folder
  const images: ImageSet = {
    alt: "Vitaliti Air step-by-step process",
    step1img1: "/images/step1.png", // Step 1: Wear the Mask
    step1img2: "/images/vitaliti-air-device.png", // Vitaliti Air device
    step2img1: "/images/step2.png", // Step 2: Breathe Naturally
    step2img2: "/images/person1.png", // Person using device
    step3img: "/images/step3.png", // Step 3: Track Your Progress
  }

  return (
    <section 
      id="how-it-works"
      className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center"
      style={{ backgroundColor: '#000000' }}
    >
      <motion.div
        ref={ref}
        className="max-w-7xl mx-auto w-full"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        <motion.div
          className="text-center mb-16"
          variants={getAnimationVariants(fadeInUp)}
        >
          <h2
            className="text-3xl md:text-4xl font-bold mb-4"
            style={{ color: '#ffffff' }}
          >
            {content.title}
          </h2>
          <p 
            className="text-lg max-w-2xl mx-auto"
            style={{ color: '#d1d5db' }}
          >
            Experience the transformation in just a few simple steps
          </p>
        </motion.div>

        <motion.div
          variants={getAnimationVariants(fadeInUp)}
          transition={{ delay: 0.2 }}
        >
          <FeatureCarousel 
            image={images}
            steps={steps}
          />
        </motion.div>
      </motion.div>
    </section>
  )
}
