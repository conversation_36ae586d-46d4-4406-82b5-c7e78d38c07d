"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Modal } from "@/components/ui/modal"

interface ApplicationModalProps {
  isOpen: boolean
  onClose: () => void
  initialEmail: string
  onSubmit: (data: ApplicationData) => void
}

export interface ApplicationData {
  firstName: string
  lastName: string
  email: string
  reason: string
}



export function ApplicationModal({ isOpen, onClose, initialEmail, onSubmit }: ApplicationModalProps) {
  const [formData, setFormData] = useState<ApplicationData>({
    firstName: "",
    lastName: "",
    email: initialEmail,
    reason: ""
  })

  const [errors, setErrors] = useState<Partial<ApplicationData>>({})
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false)

  // Update email when initialEmail changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, email: initialEmail }))
  }, [initialEmail])

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        firstName: "",
        lastName: "",
        email: initialEmail,
        reason: ""
      })
      setErrors({})
      setHasAttemptedSubmit(false)
    }
  }, [isOpen, initialEmail])

  const validateForm = () => {
    const newErrors: Partial<ApplicationData> = {}

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required"
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required"
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!formData.reason) {
      newErrors.reason = "Please select a reason"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setHasAttemptedSubmit(true)

    if (validateForm()) {
      onSubmit(formData)
    }
  }

  const handleInputChange = (field: keyof ApplicationData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error when user starts typing (only if they've attempted submit)
    if (hasAttemptedSubmit && errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const isFormValid = formData.firstName.trim() && 
                     formData.lastName.trim() && 
                     formData.email.trim() && 
                     formData.reason &&
                     /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Join Waitlist">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* First Name */}
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-white mb-2 font-inter">
            First Name *
          </label>
          <input
            id="firstName"
            type="text"
            value={formData.firstName}
            onChange={(e) => handleInputChange("firstName", e.target.value)}
            className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all font-inter"
            placeholder="Enter your first name"
            required
          />
          {hasAttemptedSubmit && errors.firstName && (
            <p className="mt-1 text-sm text-red-400 font-inter">{errors.firstName}</p>
          )}
        </div>

        {/* Last Name */}
        <div>
          <label htmlFor="lastName" className="block text-sm font-medium text-white mb-2 font-inter">
            Last Name *
          </label>
          <input
            id="lastName"
            type="text"
            value={formData.lastName}
            onChange={(e) => handleInputChange("lastName", e.target.value)}
            className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all font-inter"
            placeholder="Enter your last name"
            required
          />
          {hasAttemptedSubmit && errors.lastName && (
            <p className="mt-1 text-sm text-red-400 font-inter">{errors.lastName}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-white mb-2 font-inter">
            Email Address *
          </label>
          <input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all font-inter"
            placeholder="Enter your email address"
            required
          />
          {hasAttemptedSubmit && errors.email && (
            <p className="mt-1 text-sm text-red-400 font-inter">{errors.email}</p>
          )}
        </div>

        {/* Reason */}
        <div>
          <label htmlFor="reason" className="block text-sm font-medium text-white mb-2 font-inter">
            What is the main reason for you to try Vitaliti Air? *
          </label>
          <textarea
            id="reason"
            value={formData.reason}
            onChange={(e) => handleInputChange("reason", e.target.value)}
            className="w-full px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all font-inter resize-vertical min-h-[100px]"
            placeholder="Please share your main reason for wanting to try Vitaliti Air..."
            rows={4}
            required
          />
          {hasAttemptedSubmit && errors.reason && (
            <p className="mt-1 text-sm text-red-400 font-inter">{errors.reason}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1 bg-transparent border-white/30 text-white hover:bg-white/10 font-inter"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!isFormValid}
            className="flex-1 bg-white text-black hover:bg-gray-100 font-semibold disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 font-inter"
          >
            Join Waitlist
          </Button>
        </div>
      </form>
    </Modal>
  )
}
