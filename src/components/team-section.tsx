"use client"

import { motion } from "framer-motion"
import { getSection<PERSON>ontent, TeamContent } from "@/lib/content"
import { useInViewAnimation } from "@/lib/hooks/useInViewAnimation"
import { fadeInUp, staggerContainer, getAnimationVariants } from "@/lib/animations"
import { EnhancedTeamCard } from "@/components/enhanced-team-card"

export function TeamSection() {
  const content = getSectionContent(9) as TeamContent
  const { ref, isInView } = useInViewAnimation({ threshold: 0.1 })

  return (
    <section 
      id="team"
      ref={ref}
      className="py-20 px-8 md:px-16 lg:px-24 relative overflow-hidden min-h-screen flex items-center"
      style={{ 
        background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%)'
      }}
    >
      {/* Neutral Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-40 w-96 h-96 bg-gray-200/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-96 h-96 bg-gray-300/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.1, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Neutral floating particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-gray-400/20 rounded-full"
            style={{
              left: `${15 + i * 12}%`,
              top: `${20 + (i % 3) * 25}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.4, 0.2],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.7,
            }}
          />
        ))}

        {/* Neutral decorative elements */}
        <motion.div
          className="absolute top-1/4 right-1/4 w-1 h-32 bg-gradient-to-b from-gray-400/15 to-transparent"
          animate={{
            rotate: [0, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/4 w-1 h-24 bg-gradient-to-b from-gray-300/15 to-transparent"
          animate={{
            rotate: [360, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <motion.div
        className="max-w-7xl mx-auto w-full relative z-10"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        <motion.div
          className="text-center mb-20"
          variants={getAnimationVariants(fadeInUp)}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent"
            variants={getAnimationVariants(fadeInUp)}
          >
            {content.title}
          </motion.h2>
          <motion.p
            className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
            variants={getAnimationVariants(fadeInUp)}
            transition={{ delay: 0.1 }}
          >
            Meet the world-renowned experts driving breakthrough research in longevity,
            cardiovascular health, and human performance optimization
          </motion.p>

          {/* Professional credentials summary */}
          <motion.div
            className="flex flex-wrap justify-center gap-6 mt-8 text-sm text-gray-500"
            variants={getAnimationVariants(fadeInUp)}
            transition={{ delay: 0.15 }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span>Stanford University</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span>Buck Institute</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span>National University Singapore</span>
            </div>
          </motion.div>
          
          {/* Decorative line */}
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-500 mx-auto mt-8 rounded-full"
            variants={getAnimationVariants(fadeInUp)}
            transition={{ delay: 0.2 }}
          />
        </motion.div>
        
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
          variants={staggerContainer}
        >
          {content.members.map((member, index) => (
            <motion.div
              key={member.id}
              variants={getAnimationVariants(fadeInUp)}
              transition={{ delay: index * 0.2 }}
            >
              <EnhancedTeamCard member={member} />
            </motion.div>
          ))}
        </motion.div>


      </motion.div>
    </section>
  )
}
