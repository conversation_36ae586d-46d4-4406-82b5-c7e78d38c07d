"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SparklesCore } from "@/components/ui/sparkles"
import { getSectionContent, WaitlistCTAContent } from "@/lib/content"
import { ApplicationModal, ApplicationData } from "@/components/application-modal"

export function SparklesWaitlistSection() {
  const content = getSectionContent(8) as WaitlistCTAContent
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [emailError, setEmailError] = useState("")
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false)

  const handleInitialSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setHasAttemptedSubmit(true)

    if (!email.trim()) {
      setEmailError("Email is required")
      return
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setEmailError("Please enter a valid email address")
      return
    }

    // Clear error and open modal
    setEmailError("")
    setIsModalOpen(true)
  }

  const handleApplicationSubmit = (applicationData: ApplicationData) => {
    // Handle the complete application submission here
    console.log("Application submitted:", applicationData)

    // Close modal and show success state
    setIsModalOpen(false)
    setIsSubmitted(true)

    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setEmail("")
    }, 3000)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    // Reset the email form when modal is closed
    setEmail("")
    setEmailError("")
    setHasAttemptedSubmit(false)
  }

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value)
    // Clear error when user starts typing (only if they've attempted submit)
    if (hasAttemptedSubmit && emailError) {
      setEmailError("")
    }
  }

  return (
    <section id="waitlist" className="relative min-h-screen w-full bg-black flex flex-col items-center justify-center overflow-hidden">
      {/* Sparkles Background */}
      <div className="w-full absolute inset-0 h-screen">
        <SparklesCore
          id="tsparticleswaitlist"
          background="transparent"
          minSize={0.6}
          maxSize={1.4}
          particleDensity={100}
          className="w-full h-full"
          particleColor="#FFFFFF"
          speed={1}
        />
      </div>

      {/* Content */}
      <div className="relative z-20 max-w-4xl mx-auto w-full text-center px-8 md:px-16 lg:px-24">
        <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400 font-inter">
          Join Waitlist
        </h2>

        <p className="text-lg md:text-xl mb-12 max-w-2xl mx-auto text-neutral-300 font-inter">
          {content.subheadline}
        </p>

        {!isSubmitted ? (
          <form onSubmit={handleInitialSubmit} className="max-w-md mx-auto mb-8">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="email"
                  value={email}
                  onChange={handleEmailChange}
                  placeholder={content.emailPlaceholder}
                  className="w-full h-12 px-4 py-3 rounded-lg border-2 border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:border-white/50 transition-all font-inter"
                  required
                />
                {hasAttemptedSubmit && emailError && (
                  <p className="mt-1 text-sm text-red-400 font-inter">{emailError}</p>
                )}
              </div>
              <Button
                type="submit"
                className="bg-white text-black hover:bg-gray-100 font-semibold px-8 h-12 whitespace-nowrap transition-all duration-300 hover:scale-105 font-inter"
              >
                {content.button}
              </Button>
            </div>
          </form>
        ) : (
          <div className="max-w-md mx-auto mb-8">
            <div className="success-message p-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="success-icon mb-4 flex justify-center">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-white font-inter">
                {content.successTitle}
              </h3>
              <p className="text-white/90 font-inter">
                {content.successMessage}
              </p>
            </div>
          </div>
        )}

        <p className="text-sm text-white/80 font-inter">
          {content.text}
        </p>
      </div>

      {/* Application Modal */}
      <ApplicationModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        initialEmail={email}
        onSubmit={handleApplicationSubmit}
      />
    </section>
  )
}
