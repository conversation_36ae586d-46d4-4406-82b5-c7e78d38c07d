"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"

interface TeamMember {
  id: number
  name: string
  title: string
  photo: string
}

interface EnhancedTeamCardProps {
  member: TeamMember
}

export function EnhancedTeamCard({ member }: EnhancedTeamCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Neutral color scheme for all team members
  const neutralScheme = {
    gradient: "from-gray-400 to-gray-500",
    glowColor: "rgba(107, 114, 128, 0.3)",
    ringColor: "ring-gray-400/30",
    bgGradient: "from-gray-50/80 to-gray-100/80"
  }

  return (
    <motion.div
      className="group relative"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: 1.02, y: -8 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Floating glow effect */}
      <motion.div
        className={`absolute -inset-4 bg-gradient-to-br ${neutralScheme.gradient} rounded-3xl opacity-0 blur-xl`}
        animate={{
          opacity: isHovered ? 0.15 : 0,
          scale: isHovered ? 1.1 : 1,
        }}
        transition={{ duration: 0.3 }}
      />

      {/* Main card with fixed dimensions */}
      <div className="relative bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/60 overflow-hidden w-full h-[520px] flex flex-col">
        {/* Neutral gradient overlay */}
        <div className={`absolute inset-0 bg-gradient-to-br ${neutralScheme.bgGradient} opacity-0 transition-opacity duration-500 ${isHovered ? 'opacity-100' : ''}`}></div>

        {/* Subtle border glow effect */}
        <div className={`absolute inset-0 rounded-3xl transition-all duration-500 ${isHovered ? `shadow-lg` : ''}`} style={{ boxShadow: isHovered ? `0 10px 25px ${neutralScheme.glowColor}` : '' }}></div>

        <div className="relative z-10 flex flex-col items-center text-center space-y-6 flex-1 justify-center">
          {/* Enhanced Profile Picture */}
          <div className="relative">
            <motion.div
              className={`relative w-36 h-36 rounded-full overflow-hidden border-4 border-white shadow-xl ${neutralScheme.ringColor} ring-4 ring-offset-4 ring-offset-white/50`}
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <Image
                src={member.photo}
                alt={`${member.name} photo`}
                width={160}
                height={160}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />
              
              {/* Image overlay effect */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </motion.div>

            {/* Expertise indicator */}
            <motion.div
              className={`absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-br ${neutralScheme.gradient} rounded-full shadow-lg flex items-center justify-center`}
              whileHover={{ scale: 1.2, rotate: 10 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="white"/>
              </svg>
            </motion.div>

            {/* Pulse ring animation */}
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-gray-400 opacity-0"
              animate={{
                scale: isHovered ? [1, 1.2, 1] : 1,
                opacity: isHovered ? [0, 0.4, 0] : 0,
              }}
              transition={{
                duration: 2,
                repeat: isHovered ? Infinity : 0,
                ease: "easeInOut"
              }}
            />
          </div>

          {/* Enhanced Name and Title */}
          <div className="space-y-3">
            <motion.h3 
              className="text-2xl md:text-3xl font-bold text-gray-900 leading-tight"
              animate={{
                color: isHovered ? "#1f2937" : "#111827",
              }}
              transition={{ duration: 0.3 }}
            >
              {member.name}
            </motion.h3>
            
            <motion.p 
              className="text-sm md:text-base leading-relaxed text-gray-600 max-w-sm mx-auto"
              animate={{
                color: isHovered ? "#4b5563" : "#6b7280",
              }}
              transition={{ duration: 0.3 }}
            >
              {member.title}
            </motion.p>
          </div>





          {/* Decorative elements */}
          <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21Z" fill="currentColor"/>
            </svg>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
