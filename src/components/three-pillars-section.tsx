"use client"

import { getSectionContent, ThreeP<PERSON>rsContent } from "@/lib/content"
import DisplayCards from "@/components/ui/display-cards"
import { Heart, ShoppingBag, Brain } from "lucide-react"

export function ThreePillarsSection() {
  const content = getSectionContent(5) as ThreePillarsContent

  // Card data for DisplayCards component
  const displayCardsData = [
    {
      icon: <Heart className="size-4 text-red-300" />,
      title: content.pillars[0].title,
      description: content.pillars[0].description,
      date: "",
      titleClassName: "text-red-500",
      className: "[grid-area:stack] hover:-translate-y-16 hover:z-30 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration-700 hover:grayscale-0 before:left-0 before:top-0",
    },
    {
      icon: <ShoppingBag className="size-4 text-blue-300" />,
      title: content.pillars[1].title,
      description: content.pillars[1].description,
      date: "",
      titleClassName: "text-blue-500",
      className: "[grid-area:stack] translate-x-16 translate-y-10 hover:-translate-y-8 hover:z-30 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-border before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-background/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration-700 hover:grayscale-0 before:left-0 before:top-0",
    },
    {
      icon: <Brain className="size-4 text-green-300" />,
      title: content.pillars[2].title,
      description: content.pillars[2].description,
      date: "",
      titleClassName: "text-green-500",
      className: "[grid-area:stack] translate-x-32 translate-y-20 hover:-translate-y-4 hover:z-30",
    },
  ]

  return (
    <section className="py-16 md:py-32">
      <div className="@container mx-auto max-w-5xl px-6">
        <div className="text-center">
          <h2 className="text-balance text-4xl font-semibold lg:text-5xl">{content.title}</h2>
          <p className="mt-4">Experience comprehensive health benefits across three critical areas of wellness</p>
        </div>
        <div className="mt-8 md:mt-16">
          <DisplayCards cards={displayCardsData} />
        </div>
      </div>
    </section>
  )
}
