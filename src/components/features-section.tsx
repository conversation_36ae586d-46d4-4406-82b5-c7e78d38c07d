import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { getSectionContent, ProblemSolutionContent } from "@/lib/content"
import { AlertTriangle, Zap, TrendingUp } from "lucide-react"
import { ReactNode } from 'react'

export function FeaturesSection() {
  const content = getSectionContent(3) as ProblemSolutionContent

  // Card data structure with appropriate icons and content
  const cards = [
    {
      id: 0,
      title: "Cellular Degeneration",
      description: content.problem,
      icon: AlertTriangle,
    },
    {
      id: 1,
      title: "Mitochondrial Revitalization",
      description: content.solution,
      icon: Zap,
    },
    {
      id: 2,
      title: "Health Benefits",
      description: content.benefits,
      icon: TrendingUp,
    }
  ]

  return (
    <section className="py-16 md:py-32 font-inter">
      <div className="@container mx-auto max-w-5xl px-6">
        <div className="text-center">
          <h2 className="text-balance text-4xl font-semibold lg:text-5xl">{content.title}</h2>
          <p className="mt-4">Understanding the cellular aging process and how revolutionary technology can reverse its effects</p>
        </div>
        <div className="@min-4xl:max-w-full @min-4xl:grid-cols-3 mx-auto mt-8 grid max-w-sm gap-6 *:text-center md:mt-16">
          {cards.map((card) => (
            <Card key={card.id} className="group border-0 bg-muted shadow-none">
              <CardHeader className="pb-3">
                <CardDecorator>
                  <card.icon className="size-6" aria-hidden />
                </CardDecorator>

                <h3 className="mt-6 font-medium">{card.title}</h3>
              </CardHeader>

              <CardContent>
                <p className="text-sm">{card.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

const CardDecorator = ({ children }: { children: ReactNode }) => (
  <div aria-hidden className="relative mx-auto size-36 [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]">
    <div className="absolute inset-0 [--border:black] dark:[--border:white] bg-[linear-gradient(to_right,var(--border)_1px,transparent_1px),linear-gradient(to_bottom,var(--border)_1px,transparent_1px)] bg-[size:24px_24px] opacity-10"/>
    <div className="bg-background absolute inset-0 m-auto flex size-12 items-center justify-center border-t border-l">{children}</div>
  </div>
)


